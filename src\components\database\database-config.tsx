"use client"

import React, { useState } from 'react';
import { Database, TestTube, Save, AlertCircle, CheckCircle, Loader2, List, RefreshCw, HelpCircle } from 'lucide-react';
import { DatabaseConnection, DatabaseType, AuthenticationType } from '@/lib/database/types';
import { cn } from '@/lib/utils';
import { SqlServerHelp } from './sql-server-help';

interface DatabaseConfigProps {
  onConnect: (connection: DatabaseConnection) => Promise<void>;
  onTest: (connection: DatabaseConnection) => Promise<boolean>;
  isConnecting?: boolean;
  className?: string;
}

export function DatabaseConfig({ 
  onConnect, 
  onTest, 
  isConnecting = false,
  className = ""
}: DatabaseConfigProps) {
  const [config, setConfig] = useState<DatabaseConnection>({
    type: 'mysql',
    host: 'localhost',
    port: 3306,
    username: '',
    password: '',
    database: '',
    useWindowsAuth: false,
    authenticationType: 'windows'
  });

  const [testStatus, setTestStatus] = useState<{
    status: 'idle' | 'testing' | 'success' | 'error';
    message?: string;
  }>({ status: 'idle' });

  const [errors, setErrors] = useState<Partial<Record<keyof DatabaseConnection, string>>>({});

  const [availableDatabases, setAvailableDatabases] = useState<string[]>([]);
  const [loadingDatabases, setLoadingDatabases] = useState(false);
  const [showDatabaseList, setShowDatabaseList] = useState(false);
  const [showHelp, setShowHelp] = useState(false);

  // قائمة أسماء الخوادم الشائعة لـ SQL Server
  const commonServerNames = [
    'localhost',
    'localhost\\SQLEXPRESS',
    'localhost\\MSSQLSERVER',
    '.\\SQLEXPRESS',
    '(local)',
    '(local)\\SQLEXPRESS',
    'DESKTOP-*\\SQLEXPRESS', // سيتم استبدال * باسم الكمبيوتر
    'LAPTOP-*\\SQLEXPRESS'
  ];

  const handleTypeChange = (type: DatabaseType) => {
    setConfig(prev => ({
      ...prev,
      type,
      port: type === 'mysql' ? 3306 : 1433,
      host: type === 'mysql' ? 'localhost' : 'localhost\\SQLEXPRESS',
      useWindowsAuth: type === 'mssql' ? true : false,
      authenticationType: type === 'mssql' ? 'windows' : undefined
    }));
    setTestStatus({ status: 'idle' });
    setErrors({});
    setAvailableDatabases([]);
    setShowDatabaseList(false);
  };

  const handleInputChange = (field: keyof DatabaseConnection, value: string | number | boolean) => {
    setConfig(prev => ({ ...prev, [field]: value }));

    // مسح الخطأ عند التعديل
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }

    setTestStatus({ status: 'idle' });
  };

  const handleAuthenticationChange = (authType: AuthenticationType) => {
    setConfig(prev => ({
      ...prev,
      authenticationType: authType,
      useWindowsAuth: authType === 'windows',
      username: authType === 'windows' ? '' : prev.username,
      password: authType === 'windows' ? '' : prev.password
    }));
    setTestStatus({ status: 'idle' });
    setErrors({});
  };

  const validateConfig = (): boolean => {
    const newErrors: Partial<Record<keyof DatabaseConnection, string>> = {};

    if (!config.host.trim()) {
      newErrors.host = 'اسم الخادم مطلوب';
    }

    // للـ SQL Server، التحقق من المصادقة
    if (config.type === 'mssql' && config.authenticationType === 'sqlserver') {
      if (!config.username.trim()) {
        newErrors.username = 'اسم المستخدم مطلوب لـ SQL Server Authentication';
      }
    } else if (config.type === 'mysql' && !config.username.trim()) {
      newErrors.username = 'اسم المستخدم مطلوب';
    }

    if (!config.database.trim()) {
      newErrors.database = 'اسم قاعدة البيانات مطلوب';
    }

    if (config.port < 1 || config.port > 65535) {
      newErrors.port = 'رقم المنفذ يجب أن يكون بين 1 و 65535';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleTest = async () => {
    if (!validateConfig()) return;

    setTestStatus({ status: 'testing' });

    try {
      const success = await onTest(config);
      if (success) {
        setTestStatus({ 
          status: 'success', 
          message: 'تم الاتصال بنجاح!' 
        });
      } else {
        setTestStatus({ 
          status: 'error', 
          message: 'فشل في الاتصال بقاعدة البيانات' 
        });
      }
    } catch (error) {
      setTestStatus({ 
        status: 'error', 
        message: error instanceof Error ? error.message : 'خطأ غير معروف' 
      });
    }
  };

  const handleConnect = async () => {
    if (!validateConfig()) return;

    try {
      await onConnect(config);
    } catch (error) {
      setTestStatus({ 
        status: 'error', 
        message: error instanceof Error ? error.message : 'خطأ في الاتصال' 
      });
    }
  };

  const getPortPlaceholder = () => {
    return config.type === 'mysql' ? '3306' : '1433';
  };

  const loadAvailableDatabases = async () => {
    // التحقق من البيانات الأساسية
    if (!config.host.trim()) {
      setTestStatus({
        status: 'error',
        message: 'يرجى إدخال عنوان الخادم أولاً'
      });
      return;
    }

    if (config.type === 'mssql' && config.authenticationType === 'sqlserver' && !config.username.trim()) {
      setTestStatus({
        status: 'error',
        message: 'يرجى إدخال اسم المستخدم أولاً'
      });
      return;
    }

    setLoadingDatabases(true);
    setTestStatus({ status: 'idle' });

    try {
      const response = await fetch('/api/database/list-databases', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: config.type,
          host: config.host,
          port: config.port,
          username: config.username,
          password: config.password,
          useWindowsAuth: config.authenticationType === 'windows'
        }),
      });

      const result = await response.json();

      if (result.success) {
        setAvailableDatabases(result.databases);
        setShowDatabaseList(true);
        setTestStatus({
          status: 'success',
          message: `تم العثور على ${result.count} قاعدة بيانات`
        });
      } else {
        setTestStatus({
          status: 'error',
          message: result.error || 'فشل في الحصول على قواعد البيانات'
        });
      }
    } catch (error) {
      setTestStatus({
        status: 'error',
        message: error instanceof Error ? error.message : 'خطأ في الاتصال'
      });
    } finally {
      setLoadingDatabases(false);
    }
  };

  return (
    <div className={cn("bg-white rounded-lg shadow-md p-6", className)}>
      <div className="flex items-center gap-3 mb-6">
        <Database className="w-6 h-6 text-blue-600" />
        <h2 className="text-xl font-semibold text-gray-900">إعدادات قاعدة البيانات</h2>
      </div>

      <div className="space-y-6">
        {/* Server Type */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Server type:
          </label>
          <select
            value={config.type}
            onChange={(e) => handleTypeChange(e.target.value as DatabaseType)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="mssql">Database Engine</option>
            <option value="mysql">MySQL</option>
          </select>
        </div>

        {/* Server Name */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Server name:
            {config.type === 'mssql' && (
              <button
                type="button"
                onClick={() => setShowHelp(true)}
                className="ml-2 text-blue-600 hover:text-blue-800"
                title="مساعدة"
              >
                <HelpCircle className="w-4 h-4 inline" />
              </button>
            )}
          </label>
          <div className="relative">
            <input
              type="text"
              value={config.host}
              onChange={(e) => handleInputChange('host', e.target.value)}
              placeholder={config.type === 'mysql' ? 'localhost' : 'localhost\\SQLEXPRESS'}
              className={cn(
                "w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 pr-10",
                errors.host ? "border-red-500" : "border-gray-300"
              )}
              list={config.type === 'mssql' ? 'server-names' : undefined}
            />
            {config.type === 'mssql' && (
              <datalist id="server-names">
                {commonServerNames.map((serverName, index) => (
                  <option key={index} value={serverName} />
                ))}
              </datalist>
            )}
          </div>
          {errors.host && (
            <p className="mt-1 text-sm text-red-600">{errors.host}</p>
          )}
        </div>

        {/* Authentication for SQL Server */}
        {config.type === 'mssql' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Authentication:
            </label>
            <select
              value={config.authenticationType || 'windows'}
              onChange={(e) => handleAuthenticationChange(e.target.value as AuthenticationType)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="windows">Windows Authentication</option>
              <option value="sqlserver">SQL Server Authentication</option>
            </select>
          </div>
        )}

        {/* User name and Password for SQL Server Authentication */}
        {config.type === 'mssql' && config.authenticationType === 'sqlserver' && (
          <div className="space-y-4">
            {/* Username */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                User name:
              </label>
              <input
                type="text"
                value={config.username}
                onChange={(e) => handleInputChange('username', e.target.value)}
                placeholder="sa"
                className={cn(
                  "w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",
                  errors.username ? "border-red-500" : "border-gray-300"
                )}
              />
              {errors.username && (
                <p className="mt-1 text-sm text-red-600">{errors.username}</p>
              )}
            </div>

            {/* Password */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Password:
              </label>
              <input
                type="password"
                value={config.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                placeholder="password"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <div className="mt-2">
                <label className="flex items-center gap-2 text-sm text-gray-600">
                  <input
                    type="checkbox"
                    className="rounded border-gray-300"
                  />
                  Remember password
                </label>
              </div>
            </div>
          </div>
        )}

        {/* Username and Password for MySQL */}
        {config.type === 'mysql' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Port */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                رقم المنفذ
              </label>
              <input
                type="number"
                value={config.port}
                onChange={(e) => handleInputChange('port', parseInt(e.target.value) || 0)}
                placeholder={getPortPlaceholder()}
                min="1"
                max="65535"
                className={cn(
                  "w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",
                  errors.port ? "border-red-500" : "border-gray-300"
                )}
              />
              {errors.port && (
                <p className="mt-1 text-sm text-red-600">{errors.port}</p>
              )}
            </div>

            {/* Username */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                اسم المستخدم
              </label>
              <input
                type="text"
                value={config.username}
                onChange={(e) => handleInputChange('username', e.target.value)}
                placeholder="root"
                className={cn(
                  "w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",
                  errors.username ? "border-red-500" : "border-gray-300"
                )}
              />
              {errors.username && (
                <p className="mt-1 text-sm text-red-600">{errors.username}</p>
              )}
            </div>

            {/* Password */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                كلمة المرور
              </label>
              <input
                type="password"
                value={config.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                placeholder="password"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        )}

        {/* Database Name */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="block text-sm font-medium text-gray-700">
              اسم قاعدة البيانات
            </label>
            <button
              type="button"
              onClick={loadAvailableDatabases}
              disabled={loadingDatabases}
              title="اضغط لعرض قواعد البيانات المتاحة على الخادم"
              className="flex items-center gap-1 px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {loadingDatabases ? (
                <Loader2 className="w-3 h-3 animate-spin" />
              ) : (
                <List className="w-3 h-3" />
              )}
              عرض قواعد البيانات
            </button>
          </div>

          <input
            type="text"
            value={config.database}
            onChange={(e) => handleInputChange('database', e.target.value)}
            placeholder="database_name"
            className={cn(
              "w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",
              errors.database ? "border-red-500" : "border-gray-300"
            )}
          />
          {errors.database && (
            <p className="mt-1 text-sm text-red-600">{errors.database}</p>
          )}

          {/* Available Databases List */}
          {showDatabaseList && availableDatabases.length > 0 && (
            <div className="mt-3 p-3 bg-gray-50 rounded-lg border">
              <div className="flex items-center gap-2 mb-2">
                <Database className="w-4 h-4 text-gray-600" />
                <span className="text-sm font-medium text-gray-700">
                  قواعد البيانات المتاحة ({availableDatabases.length})
                </span>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 max-h-32 overflow-y-auto">
                {availableDatabases.map((dbName) => (
                  <button
                    key={dbName}
                    type="button"
                    onClick={() => handleInputChange('database', dbName)}
                    className={cn(
                      "px-2 py-1 text-xs text-left rounded border transition-colors",
                      config.database === dbName
                        ? "bg-blue-100 border-blue-300 text-blue-700"
                        : "bg-white border-gray-200 text-gray-700 hover:bg-gray-100"
                    )}
                  >
                    {dbName}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Test Status */}
        {testStatus.status !== 'idle' && (
          <div className={cn(
            "p-4 rounded-lg border",
            testStatus.status === 'testing' && "bg-blue-50 border-blue-200",
            testStatus.status === 'success' && "bg-green-50 border-green-200",
            testStatus.status === 'error' && "bg-red-50 border-red-200"
          )}>
            <div className="flex items-center gap-2">
              {testStatus.status === 'testing' && (
                <Loader2 className="w-4 h-4 animate-spin text-blue-600" />
              )}
              {testStatus.status === 'success' && (
                <CheckCircle className="w-4 h-4 text-green-600" />
              )}
              {testStatus.status === 'error' && (
                <AlertCircle className="w-4 h-4 text-red-600" />
              )}
              <span className={cn(
                "text-sm font-medium",
                testStatus.status === 'testing' && "text-blue-700",
                testStatus.status === 'success' && "text-green-700",
                testStatus.status === 'error' && "text-red-700"
              )}>
                {testStatus.status === 'testing' && "جاري اختبار الاتصال..."}
                {testStatus.status === 'success' && "نجح الاتصال"}
                {testStatus.status === 'error' && "فشل الاتصال"}
              </span>
            </div>
            {testStatus.message && (
              <p className={cn(
                "mt-1 text-sm",
                testStatus.status === 'testing' && "text-blue-600",
                testStatus.status === 'success' && "text-green-600",
                testStatus.status === 'error' && "text-red-600"
              )}>
                {testStatus.message}
              </p>
            )}
          </div>
        )}

        {/* Actions */}
        <div className="flex gap-3 pt-4 border-t">
          <button
            onClick={handleTest}
            disabled={testStatus.status === 'testing' || isConnecting}
            className="flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {testStatus.status === 'testing' ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <TestTube className="w-4 h-4" />
            )}
            اختبار الاتصال
          </button>

          <button
            onClick={handleConnect}
            disabled={testStatus.status !== 'success' || isConnecting}
            className="flex items-center gap-2 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isConnecting ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Save className="w-4 h-4" />
            )}
            {isConnecting ? 'جاري الاتصال...' : 'الاتصال وبدء التحليل'}
          </button>
        </div>
      </div>

      {/* Help Modal */}
      <SqlServerHelp
        isOpen={showHelp}
        onClose={() => setShowHelp(false)}
      />
    </div>
  );
}
